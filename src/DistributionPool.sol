// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {UUPSUpgradeable} from "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import {Initializable} from "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {PausableUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import {ReentrancyGuardUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import {AccessControlUpgradeable} from "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import {FixedPointMathLib} from "@solady/utils/FixedPointMathLib.sol";
import "forge-std/console.sol";
import "./interfaces/IUniswapV2Router02.sol";
import "./interfaces/IUniswapV2Factory.sol";
import "./interfaces/IDistributionPool.sol";
import "./IPCoin.sol";
import "./TraderNft.sol";
import "./CreatorNft.sol";

/**
 * @title DistributionPool Contract
 * @notice This is a distribution pool contract for managing IP token trading and liquidity
 * @dev This contract implements token trading, liquidity management, and NFT interaction functionalities
 */
contract DistributionPool is
    Initializable,
    UUPSUpgradeable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable,
    IDistributionPool
{
    using FixedPointMathLib for uint256;

    // Role definitions
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant ADDIP_ROLE = keccak256("ADDIP_ROLE");

    // System constants
    address private constant BlackHole = ******************************************;
    uint256 private constant SQRT_PRECISION = 1e9;
    uint256 private constant DEFAULT_PRECISION = 1e18;

    // System parameters
    uint256 public BONDING_CURVE_TRADE_CAP; // Maximum bonding curve token supply
    uint256 public PRICE_CONSTANT; // Price constant k
    uint256 private TRADE_FEE_RATIO; // Trading fee ratio
    uint256 private LIQUIDITY_TOKEN_AMOUNT; // Liquidity token amount
    uint256 private LIQUIDITY_NATIVE_AMOUNT; // Liquidity ETH/BNB amount
    uint256 public VIRTUAL_TOKEN_SUPPLY; // Virtual token supply
    uint256 public VIRTUAL_ETH_SUPPLY; // Virtual ETH supply
    uint256 public MINIMUM_ETH_TO_BUY; // Minimum ETH amount for buying

    // Address configurations
    address private FEE_RECEIVER; // Fee receiver address
    address private FACTORY_ADDR; // Factory contract address
    address private UNISWAP_FACTORY; // Uniswap factory address
    address private UNISWAP_ROUTER; // Uniswap router address

    // Maximum percentage of wallet balance that can be distributed (expressed in basis points)
    // 1000 basis points = 10%
    uint256 public MAX_WALLET_DISTRIBUTION_PERCENTAGE;

    // Halving algorithm parameters
    uint256 public REWARD_TOKEN_SUPPLY;
    uint256 public INITIAL_MAX_OUTPUT_PER_INTERVAL;
    uint256 public HALVING_THRESHOLD;

    uint256 private totalEthFee; // Total unclaimed ETH fees
    uint256 public currentVersion;

    // Global reward interval (default: 1 week)
    // fixme: 注意，这是一个全局的参数，目前 base 测试环境允许其为 1 会使 agent 获得分配的频率也解除控制
    uint256 public globalRewardInterval;

    // Main mapping for all coin pool data
    mapping(address => CoinPoolData) public coinPoolsData;

    // Mapping to track last reward received timestamp by recipient for a specific coin _coin -> recipient -> timestamp
    mapping(address => mapping(address => uint256)) public lastRewardReceivedTimestamp;

    // Whitelist for reward distribution
    mapping(address => bool) public creatorRewardDistributorsWhitelist;

    // Struct for reward distribution
    struct RewardDistribution {
        address recipient;
        uint256 amount;
    }

    // Coin Pool Data Struct
    struct CoinPoolData {
        IPCoin ipCoinContract; // IP token contract
        TraderNft traderNftContract; // Trader NFT contract
        CreatorNft creatorNftContract; // Creator NFT contract
        bool allowTrade; // Token trading permission
        bool tradeNftStatus; // NFT trading status
        address pairAddress; // Token pair address
        address aiWallet; // AI wallet address
        uint256 poolVersion; // Pool version (previously ipVersions)
        uint256 tokenAmountInPool; // Token amount in pool (previously tokensAmounts)
        uint256 nativeAmountInPool; // Native token (ETH/BNB) amount in pool (previously coinsNativeAmount)
        uint256 intervalRewardDistributionTotal; // Interval reward distribution total
        uint256 epochCreatorRewardDistributionTotal; // Epoch creator reward distribution total
        uint256 lastDistributionTimestamp; // Last distribution timestamp
        uint256 lastCreatorDistributionTimestamp; // Last creator distribution timestamp
        uint256 cumulativeDistribution; // Cumulative distribution
        uint256 currentPhase; // Current halving phase
        uint256 specificRewardInterval; // Specific reward interval (previously rewardIntervals)
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initialize the contract
     * @param _admin Admin address
     * @param _feeReceiver Fee receiver address
     * @param factory Factory contract address
     */
    function initialize(address _admin, address _feeReceiver, address factory) public initializer {
        __Pausable_init();
        __AccessControl_init();

        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
        _grantRole(ADDIP_ROLE, factory);

        LIQUIDITY_TOKEN_AMOUNT = 200_000_000 * DEFAULT_PRECISION;
        TRADE_FEE_RATIO = 3 * 1e16; // 3%
        MINIMUM_ETH_TO_BUY = 0.0001 ether;

        FEE_RECEIVER = _feeReceiver;
        FACTORY_ADDR = factory;

        currentVersion = 20_250_301;

        // 设置通用参数
        _setCommonParameters();

        // https://docs.base.org/docs/contracts/
        // https://developer.pancakeswap.finance/contracts/v2/factory-v2
        if (block.chainid == 8453) {
            // base mainnet
            UNISWAP_FACTORY = ******************************************;
            UNISWAP_ROUTER = ******************************************;

            // 设置特定链的参数
        } else if (block.chainid == 84_532) {
            // base sepolia
            UNISWAP_FACTORY = ******************************************;
            UNISWAP_ROUTER = ******************************************;

            // 设置特定链的参数
        } else if (block.chainid == 97) {
            // bsc testnet
            UNISWAP_FACTORY = ******************************************;
            UNISWAP_ROUTER = 0xD99D1c33F9fC3444f8101754aBC46c52416550D1;

            // 设置特定链的参数
        } else if (block.chainid == 56) {
            // bsc mainnet
            UNISWAP_FACTORY = 0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73;
            UNISWAP_ROUTER = 0x10ED43C718714eb63d5aA57B78B54704E256024E;
        } else {
            revert UnsupportedChain();
        }

        // 设置特定链的参数
        _setChainSpecificParameters();
    }

    // ------------------------ User Functions -----------------------------------

    /**
     * @notice Buy tokens
     * @param _coin Token address
     * @param minTokensToBuy Minimum amount of tokens to buy
     */
    function buy(
        address _coin,
        address recipient,
        uint256 minTokensToBuy
    ) external payable whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-buy checks
        require(poolData.allowTrade, FeatureDisabled());
        require(poolData.tokenAmountInPool <= BONDING_CURVE_TRADE_CAP, MaxCapReached());
        require(msg.value >= MINIMUM_ETH_TO_BUY, MinimumTransactionLimit());
        require(recipient != address(this), "Invalid recipient");

        // Calculate token amount to buy
        uint256 tokensToBuy = calculateTokenAmount(_coin, msg.value);
        // Confirm token amount is valid
        require(tokensToBuy > 0, InvalidAmount());
        require(tokensToBuy >= minTokensToBuy, SlippageExceeded());
        require(poolData.tokenAmountInPool + tokensToBuy <= BONDING_CURVE_TRADE_CAP, MaxCapReached());

        // Calculate buyer fee
        uint256 buyerFee = FixedPointMathLib.mulWad(msg.value, TRADE_FEE_RATIO);

        // Update token amounts
        poolData.tokenAmountInPool = poolData.tokenAmountInPool + tokensToBuy;
        poolData.nativeAmountInPool = poolData.nativeAmountInPool + msg.value - buyerFee;
        totalEthFee = totalEthFee + buyerFee;

        // Disable trading if bonding curve is 100% complete
        if (poolData.tokenAmountInPool >= BONDING_CURVE_TRADE_CAP) {
            addLiquidityToPool(_coin);
        }

        // If Trader NFT is enabled, mint token and record transaction
        uint256 tokenId = 0;
        if (poolData.tradeNftStatus) {
            // todo:
            // tokenId = poolData.traderNftContract.safeMint(recipient, tokensToBuy);
        } else {
            poolData.ipCoinContract.transfer(recipient, tokensToBuy);
        }

        emit Buy(msg.sender, recipient, _coin, tokensToBuy, msg.value, tokenId, poolData.tradeNftStatus);
    }

    /**
     * @notice Sell tokens
     * @param _coin Token address
     * @param tokenAmount Amount of tokens to sell
     * @param minEthToReturn Minimum ETH amount to return
     */
    function sell(address _coin, uint256 tokenAmount, uint256 minEthToReturn) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-sell checks
        require(poolData.allowTrade, FeatureDisabled());
        require(tokenAmount > 0, InvalidAmount());
        // todo: 可以看看这个限制能否去除，但还是要以安全性为第一优先考虑
        require(tokenAmount > SQRT_PRECISION, InvalidAmount());

        uint256 userCoinBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        require(userCoinBalance >= tokenAmount, InsufficientBalance());

        uint256 allowance = poolData.ipCoinContract.allowance(msg.sender, address(this));
        require(allowance >= tokenAmount, InsufficientAllowance());

        (uint256 ethToReturn, uint256 sellerFee) = _calculateETHAmount(_coin, tokenAmount);

        // Confirm ETH amount to return is valid
        require(ethToReturn >= minEthToReturn, SlippageExceeded());
        require(poolData.nativeAmountInPool > ethToReturn, InvalidAmount());
        require(address(this).balance > ethToReturn, InvalidAmount());

        // Update token amounts
        poolData.tokenAmountInPool = poolData.tokenAmountInPool - tokenAmount;
        poolData.nativeAmountInPool = poolData.nativeAmountInPool - (ethToReturn + sellerFee);
        totalEthFee = totalEthFee + sellerFee;

        // transfer form to pool
        require(poolData.ipCoinContract.transferFrom(msg.sender, address(this), tokenAmount), TransferFailed());

        // Transfer ETH to seller
        (bool success,) = payable(msg.sender).call{value: ethToReturn}("");
        require(success, TransferFailed());

        emit Sell(_coin, msg.sender, tokenAmount, ethToReturn);
    }

    /**
     * @notice Add liquidity to Uniswap pool
     * @param _coin Token address
     * @return Liquidity pool address
     */
    function addLiquidityToPool(
        address _coin
    ) internal whenNotPaused returns (address) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Pre-liquidity checks
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        require(poolData.allowTrade, FeatureDisabled());
        require(poolData.nativeAmountInPool >= LIQUIDITY_NATIVE_AMOUNT, InsufficientLiquidity());
        require(address(this).balance >= LIQUIDITY_NATIVE_AMOUNT, InsufficientBalance());
        require(poolData.tokenAmountInPool == BONDING_CURVE_TRADE_CAP, InsufficientBalance());

        // Mint tokens for liquidity
        poolData.ipCoinContract.approve(UNISWAP_ROUTER, LIQUIDITY_TOKEN_AMOUNT);
        IUniswapV2Router02 router = IUniswapV2Router02(UNISWAP_ROUTER);

        // Add liquidity with ETH
        router.addLiquidityETH{value: LIQUIDITY_NATIVE_AMOUNT}(
            _coin,
            LIQUIDITY_TOKEN_AMOUNT,
            LIQUIDITY_TOKEN_AMOUNT, // slippage is unavoidable
            LIQUIDITY_NATIVE_AMOUNT, // slippage is unavoidable
            BlackHole,
            block.timestamp + 300
        );

        poolData.allowTrade = false;
        // Collect listing fee (approximately 0.2 ETH)
        // @dev When the Bonding curve reaches 100%, the part that exceeds the expected number of eth is the part of the deposit fee
        totalEthFee = totalEthFee + (poolData.nativeAmountInPool - LIQUIDITY_NATIVE_AMOUNT);
        // Update token amounts
        poolData.nativeAmountInPool = 0;
        poolData.tokenAmountInPool = poolData.tokenAmountInPool + LIQUIDITY_TOKEN_AMOUNT;

        poolData.ipCoinContract.setListed(true);

        emit TokenListedToDex(_coin, poolData.pairAddress);

        return poolData.pairAddress;
    }

    // ------------------------ Bonding Curve Compute Logic -----------------------------------

    /**
     * @notice Calculate token amount for given ETH amount
     * @dev Uses Bonding Curve formula, considering fees and maximum supply
     * @param _coin Token address
     * @param ethAmount ETH amount
     * @return Token amount
     */
    function calculateTokenAmount(address _coin, uint256 ethAmount) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (ethAmount == 0) return 0;
        if (!poolData.allowTrade) {
            return 0;
        }

        uint256 buyerFee = FixedPointMathLib.mulWad(ethAmount, TRADE_FEE_RATIO);

        ethAmount = ethAmount - buyerFee;

        uint256 ethAmountWithVirtual = poolData.nativeAmountInPool + VIRTUAL_ETH_SUPPLY;

        // Calculate raw token amount
        uint256 rawTokenAmount = _rawCalculateTokens(ethAmountWithVirtual, ethAmount);

        // Check max token cap
        if (rawTokenAmount + poolData.tokenAmountInPool > BONDING_CURVE_TRADE_CAP) {
            rawTokenAmount = BONDING_CURVE_TRADE_CAP - poolData.tokenAmountInPool;
        }

        return rawTokenAmount;
    }

    /**
     * @notice Calculate ETH amount for given token amount
     * @dev Internal function, uses Bonding Curve formula, considering fees and pool balance
     * @param _coin Token address
     * @param tokenAmount Token amount
     * @return finalAmount Final ETH amount
     * @return fee Fee amount
     */
    function _calculateETHAmount(address _coin, uint256 tokenAmount) private view returns (uint256, uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        if (tokenAmount == 0) return (0, 0);
        if (!poolData.allowTrade) {
            return (0, 0);
        }
        if (poolData.tokenAmountInPool < tokenAmount) revert InsufficientBalance();

        uint256 tokenAmountWithVirtual = poolData.tokenAmountInPool + VIRTUAL_TOKEN_SUPPLY;

        // Calculate raw ETH amount
        uint256 rawEthAmount = _rawCalculateEth(tokenAmountWithVirtual, tokenAmount);

        // Calculate fee
        uint256 fee = FixedPointMathLib.mulWad(rawEthAmount, TRADE_FEE_RATIO);
        // uint256 fee = (rawEthAmount * 3) / 100;
        // Calculate final amount
        uint256 finalAmount = rawEthAmount - fee;

        // Ensure we don't exceed pool's ETH balance
        if (finalAmount >= poolData.nativeAmountInPool) {
            // This branch handles an extreme fallback scenario where the finalAmount
            // computed by the bonding curve exceeds the current coinEth balance.
            // Under normal circumstances, this should never happen if all parameters
            // are correctly scaled and the pool balance is maintained.
            // If this occurs, we recalculate the payout and fee based on the current
            // pool balance to prevent over-withdrawal of ETH.
            if (tokenAmount < poolData.tokenAmountInPool) {
                revert InsufficientBalance();
            }
            fee = FixedPointMathLib.mulWad(poolData.nativeAmountInPool, TRADE_FEE_RATIO);
            finalAmount = poolData.nativeAmountInPool - fee;
        }

        return (finalAmount, fee);
    }

    /**
     * @notice Calculate raw token amount using Bonding Curve
     * @dev Uses square root formula
     * @param currentEthAmount Current ETH amount
     * @param ethAmount ETH amount
     * @return Token amount
     */
    function _rawCalculateTokens(uint256 currentEthAmount, uint256 ethAmount) private view returns (uint256) {
        uint256 sqrtAfter = FixedPointMathLib.sqrtWad(currentEthAmount + ethAmount);
        uint256 sqrtBefore = FixedPointMathLib.sqrtWad(currentEthAmount);

        uint256 difference = sqrtAfter - sqrtBefore;
        uint256 divided = FixedPointMathLib.divWad(difference, PRICE_CONSTANT);

        return 2 * divided;
    }

    /**
     * @notice Calculate raw ETH amount using Bonding Curve
     * @dev Uses quadratic equation
     * @param currentTokens Current token amount
     * @param tokenAmount Token amount
     * @return ETH amount
     */
    function _rawCalculateEth(uint256 currentTokens, uint256 tokenAmount) private view returns (uint256) {
        uint256 meiotic = 2 * FixedPointMathLib.mulWad(currentTokens, tokenAmount);
        uint256 minuend = FixedPointMathLib.mulWad(tokenAmount, tokenAmount);
        uint256 afterSub = meiotic - minuend;
        uint256 waitDiv1 = FixedPointMathLib.mulWad(afterSub, PRICE_CONSTANT);
        uint256 dividend = FixedPointMathLib.mulWad(waitDiv1, PRICE_CONSTANT);
        uint256 result = dividend / 4;

        return result;
    }

    // ------------------------ Public Query Functions -----------------------------------

    /**
     * @notice Calculate ETH amount for given token amount
     * @dev Public query function, returns ETH amount considering fees
     * @param _coin Token address
     * @param tokenAmount Token amount
     * @return ETH amount
     */
    function calculateEthAmount(address _coin, uint256 tokenAmount) public view returns (uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (!poolData.allowTrade) {
            return 0;
        }
        (uint256 ethAmount,) = _calculateETHAmount(_coin, tokenAmount);
        return ethAmount;
    }

    /**
     * @notice Get token's ETH amount
     * @param _coin Token address
     * @return Token's ETH amount
     */
    function getCoinEthAmount(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].nativeAmountInPool;
    }

    /**
     * @notice Get token's trading status
     * @param _coin Token address
     * @return Token's trading status
     */
    function getCoinTradeStatus(
        address _coin
    ) external view returns (bool) {
        return coinPoolsData[_coin].allowTrade;
    }

    /**
     * @notice Get token's total amount
     * @param _coin Token address
     * @return Token's total amount
     */
    function getCoinTokenAmount(
        address _coin
    ) external view returns (uint256) {
        return coinPoolsData[_coin].tokenAmountInPool;
    }

    /**
     * @notice Get token's capacity and supply
     * @dev Returns total capacity and supply, including virtual supply
     * @param _coin Token address
     * @return Token's capacity and supply
     */
    function getCapAndSupply(
        address _coin
    ) external view returns (uint256, uint256) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        if (!poolData.allowTrade) {
            return (LIQUIDITY_NATIVE_AMOUNT, poolData.ipCoinContract.totalSupply());
        }
        return (poolData.nativeAmountInPool + VIRTUAL_ETH_SUPPLY, poolData.ipCoinContract.totalSupply());
    }

    /**
     * @notice Get fee receiver address
     * @return Fee receiver address
     */
    function getFeeReceiver() external view returns (address) {
        return FEE_RECEIVER;
    }

    /**
     * @notice Get total ETH fees
     * @return Total ETH fees
     */
    function getTotalEthFee() external view returns (uint256) {
        return totalEthFee;
    }

    /**
     * @notice Check if account has admin role
     * @param account Account address
     * @return If account has admin role
     */
    function checkAdmin(
        address account
    ) external view returns (bool) {
        return hasRole(ADMIN_ROLE, account);
    }

    /**
     * @notice Set maximum percentage of wallet balance that can be distributed
     * @dev Only callable by admin role
     * @param percentage New maximum percentage in basis points (100 = 1%)
     */
    function setMaxWalletDistributionPercentage(
        uint256 percentage
    ) external onlyRole(ADMIN_ROLE) {
        require(percentage > 0 && percentage <= 10_000, "Invalid percentage"); // Max 100% (10000 basis points)
        MAX_WALLET_DISTRIBUTION_PERCENTAGE = percentage;
        emit MaxWalletDistributionPercentageUpdated(percentage);
    }

    // ------------------------ Admin Functions -----------------------------------
    /**
     * @notice Add IP token
     * @dev Only callable by ADDIP_ROLE
     * @param _coin Token address
     */
    function addIp(address _coin, address _creatorNft, address _aiAgentWallet) external onlyRole(ADDIP_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) == address(0), CoinAlreadyExists());

        poolData.ipCoinContract = IPCoin(_coin);
        poolData.tokenAmountInPool = 0;
        poolData.nativeAmountInPool = 0;
        poolData.allowTrade = true;
        poolData.tradeNftStatus = false;
        poolData.creatorNftContract = CreatorNft(_creatorNft);
        poolData.aiWallet = _aiAgentWallet;
        poolData.poolVersion = currentVersion;

        address pair = IUniswapV2Factory(UNISWAP_FACTORY).createPair(_coin, IUniswapV2Router02(UNISWAP_ROUTER).WETH());
        poolData.pairAddress = pair;

        poolData.ipCoinContract.mint(address(this), 1_000_000_000 * DEFAULT_PRECISION);

        poolData.ipCoinContract.initPair(pair);

        emit CoinAdded(_coin, pair);
    }

    /**
     * @notice Set token's NFT status
     * @dev Only callable by ADMIN_ROLE
     * @param _coin Token address
     * @param _nft NFT contract address
     */
    function addTraderNft(address _coin, address _nft) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        poolData.traderNftContract = TraderNft(_nft);
        poolData.tradeNftStatus = true;

        emit TraderNftAdded(_coin, _nft);
    }

    /**
     * @notice Set fee receiver address
     * @dev Only callable by ADMIN_ROLE
     * @param _feeReceiver Fee receiver address
     */
    function setFeeReceiver(
        address _feeReceiver
    ) external onlyRole(ADMIN_ROLE) {
        FEE_RECEIVER = _feeReceiver;
    }

    /**
     * @notice Withdraw ETH fees
     * @dev Only callable by ADMIN_ROLE
     */
    function withdrawFee() external onlyRole(ADMIN_ROLE) {
        require(totalEthFee > 0, NoFeesAvailable());

        (bool success,) = payable(FEE_RECEIVER).call{value: totalEthFee}("");
        require(success, TransferFailed());

        totalEthFee = 0;
    }

    /**
     * @notice Update factory contract address
     * @dev Only callable by ADMIN_ROLE
     * @param factory New factory contract address
     */
    function setFactory(
        address factory
    ) external onlyRole(ADMIN_ROLE) {
        require(factory != address(0), "Factory address cannot be zero");
        if (hasRole(ADDIP_ROLE, FACTORY_ADDR)) {
            _revokeRole(ADDIP_ROLE, FACTORY_ADDR);
        }
        FACTORY_ADDR = factory;
        _grantRole(ADDIP_ROLE, FACTORY_ADDR);
    }

    function removeAdmin(
        address _oldAdmin
    ) external onlyRole(ADMIN_ROLE) {
        require(_oldAdmin != address(0), "Invalid admin address");
        require(_oldAdmin != msg.sender, "You cannot remove yourself");

        _revokeRole(ADMIN_ROLE, _oldAdmin);
    }

    function addAdmin(
        address _newAdmin
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(ADMIN_ROLE, _newAdmin);
    }

    function addPauser(
        address _newPauser
    ) external onlyRole(ADMIN_ROLE) {
        _grantRole(PAUSER_ROLE, _newPauser);
    }

    function removePauser(
        address _oldPauser
    ) external onlyRole(ADMIN_ROLE) {
        _revokeRole(PAUSER_ROLE, _oldPauser);
    }

    /**
     * @notice Set minimum ETH amount for buying
     * @dev Only callable by ADMIN_ROLE
     * @param _minimumToBuy Minimum ETH amount for buying
     */
    function setMinimumToBuy(
        uint256 _minimumToBuy
    ) external onlyRole(ADMIN_ROLE) {
        MINIMUM_ETH_TO_BUY = _minimumToBuy;
    }

    /**
     * @notice Set liquidity ETH amount
     * @dev Only callable by ADMIN_ROLE
     * @param _liquidityEthAmount Liquidity ETH amount
     */
    function setLiquidityEthAmount(
        uint256 _liquidityEthAmount
    ) external onlyRole(ADMIN_ROLE) {
        LIQUIDITY_NATIVE_AMOUNT = _liquidityEthAmount;
    }

    /**
     * @notice Pause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause contract
     * @dev Only callable by PAUSER_ROLE
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    function migrateNewVersion() external onlyRole(ADMIN_ROLE) {
        uint256 newVersion = 20_250_301;

        // fixme:
        // if (currentVersion == newVersion) {
        //     return;
        // }

        // 设置通用参数
        _setCommonParameters();

        currentVersion = newVersion;

        // 设置特定链的参数
        _setChainSpecificParameters();
    }

    function migratePool(address _coin, address _creatorNft, address _aiWallet) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        // require(poolData.poolVersion < currentVersion, "Pool version is up to date");

        uint256 currentSupply = poolData.ipCoinContract.totalSupply();
        uint256 mintAmount = 1_000_000_000 * DEFAULT_PRECISION - currentSupply;

        if (mintAmount > 0) {
            poolData.ipCoinContract.mint(address(this), mintAmount);
        }

        poolData.poolVersion = currentVersion;
        poolData.creatorNftContract = CreatorNft(_creatorNft);
        poolData.aiWallet = _aiWallet;

        poolData.ipCoinContract.initPair(poolData.pairAddress);
        poolData.ipCoinContract.setListed(!poolData.allowTrade);

        emit PoolMigrated(_coin, currentSupply, mintAmount, currentVersion);
    }

    // ------------------------ Reward Distribution Functions -----------------------------------

    /**
     * @notice Add an address to the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to add to whitelist
     */
    function addRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = true;
        emit RewardDistributorAdded(distributor);
    }

    /**
     * @notice Remove an address from the reward distributors whitelist
     * @dev Only callable by ADMIN_ROLE
     * @param distributor Address to remove from whitelist
     */
    function removeRewardDistributor(
        address distributor
    ) external onlyRole(ADMIN_ROLE) {
        creatorRewardDistributorsWhitelist[distributor] = false;
        emit RewardDistributorRemoved(distributor);
    }

    /**
     * @notice Set reward interval for a specific coin
     * @dev Only callable by ADMIN_ROLE, when set to 0 it will use the global interval
     * @param _coin IPCoin address
     * @param interval New reward interval in seconds, or 0 to use global interval
     */
    function setRewardInterval(address _coin, uint256 interval) external onlyRole(ADMIN_ROLE) {
        CoinPoolData storage poolData = coinPoolsData[_coin];
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());
        poolData.specificRewardInterval = interval;
        emit RewardIntervalUpdated(_coin, interval);
    }

    /**
     * @notice Set the global reward interval that applies to all coins without specific intervals
     * @dev Only callable by ADMIN_ROLE
     * @param interval New global reward interval in seconds
     */
    function setGlobalRewardInterval(
        uint256 interval
    ) external onlyRole(ADMIN_ROLE) {
        require(interval > 0, "Interval must be greater than zero");
        globalRewardInterval = interval;
        emit GlobalRewardIntervalUpdated(interval);
    }

    /**
     * @notice Distribute rewards to ai agent and creator
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @param _coin IPCoin address
     * @param amount Amount to distribute
     */
    function distributeCreatorRewards(address _coin, uint256 amount) external whenNotPaused nonReentrant {
        // Check if caller is whitelisted
        require(creatorRewardDistributorsWhitelist[msg.sender], "Not authorized");

        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if time interval has passed for this group (uses global interval if coin-specific is not set)
        uint256 interval = poolData.specificRewardInterval > 0 ? poolData.specificRewardInterval : globalRewardInterval;
        require(
            block.timestamp >= poolData.lastCreatorDistributionTimestamp + interval
                || poolData.lastCreatorDistributionTimestamp == 0,
            "Time interval limit: one distribution per interval"
        );

        // Calculate maximum allowed output based on halving algorithm
        uint256 maxIntervalOutput;
        if (poolData.cumulativeDistribution < HALVING_THRESHOLD * (2 ** poolData.currentPhase)) {
            maxIntervalOutput = INITIAL_MAX_OUTPUT_PER_INTERVAL / (2 ** poolData.currentPhase);
        } else {
            poolData.currentPhase += 1;
            maxIntervalOutput = INITIAL_MAX_OUTPUT_PER_INTERVAL / (2 ** poolData.currentPhase);
            emit PhaseIncreased(_coin, poolData.currentPhase, maxIntervalOutput);
        }

        // Make sure distribution amount doesn't exceed the calculated maximum
        require(amount <= maxIntervalOutput, "Exceeds maximum interval production rate");

        // Check if total distribution exceeds epoch limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset epoch total if we're in a new epoch
        if (currentEpochStart > poolData.lastCreatorDistributionTimestamp) {
            poolData.epochCreatorRewardDistributionTotal = 0;
        }

        // Update epoch total and cumulative distribution
        poolData.epochCreatorRewardDistributionTotal += amount;
        poolData.cumulativeDistribution += amount;

        // Update last distribution timestamp
        poolData.lastCreatorDistributionTimestamp = block.timestamp;

        // Get creator address - through the holder of NFT tokenId 0
        address creator;
        try poolData.creatorNftContract.ownerOf(0) returns (address owner) {
            creator = owner;
        } catch {
            revert();
        }

        // Allocate tokens according to the new rules: 50% to creators, 10% to the platform, 40% to the AI wallet.
        uint256 creatorAmount = (amount * 50) / 100; // 50% creators
        uint256 platformAmount = (amount * 10) / 100; // 10% platform
        uint256 aiWalletAmount = amount - creatorAmount - platformAmount; // 40% to AI wallet

        poolData.ipCoinContract.transfer(creator, creatorAmount);
        poolData.ipCoinContract.transfer(FEE_RECEIVER, platformAmount);
        poolData.ipCoinContract.transfer(poolData.aiWallet, aiWalletAmount);

        emit CreatorRewardsDistributed(msg.sender, _coin, amount);
    }

    // todo: 引入一个 epoch 或者 era 的概念，在该方法 emit event 时把 epoch/era number 也写到链上
    /**
     * @notice Distribute rewards to multiple addresses
     * @dev Only callable by whitelisted distributors, once per interval per group
     * @dev Limited to a maximum of MAX_WALLET_DISTRIBUTION_PERCENTAGE of current wallet balance
     * @param _coin IPCoin address
     * @param distributions Array of reward distributions (max 10 recipients)
     */
    function distributeRewards(
        address _coin,
        RewardDistribution[] calldata distributions
    ) external whenNotPaused nonReentrant {
        CoinPoolData storage poolData = coinPoolsData[_coin];

        // Check if IPCoin exists
        require(address(poolData.ipCoinContract) != address(0), CoinNotExists());

        // Check if caller is whitelisted
        require(poolData.aiWallet == msg.sender, "Not authorized");

        // Check maximum number of recipients
        require(distributions.length <= 10, "Too many recipients");

        // Calculate total distribution amount
        uint256 totalDistribution = 0;
        for (uint256 i = 0; i < distributions.length; i++) {
            totalDistribution += distributions[i].amount;
        }

        // Define the distribution interval, used for periodic limits like interval total
        uint256 interval = poolData.specificRewardInterval > 0 ? poolData.specificRewardInterval : globalRewardInterval;

        // Check if total distribution exceeds interval limit
        uint256 currentEpochStart = block.timestamp - (block.timestamp % interval);

        // Reset interval total if we're in a new interval
        if (currentEpochStart > poolData.lastDistributionTimestamp) {
            poolData.intervalRewardDistributionTotal = 0;
        }

        // Check if total distribution exceeds maximum percentage of wallet balance
        uint256 currentWalletBalance = poolData.ipCoinContract.balanceOf(msg.sender);
        uint256 maxDistributionAmount = (currentWalletBalance * MAX_WALLET_DISTRIBUTION_PERCENTAGE) / 10_000; // Convert basis points to percentage

        require(totalDistribution < maxDistributionAmount, "Exceeds maximum wallet distribution percentage");

        // Update interval total
        poolData.intervalRewardDistributionTotal += totalDistribution;

        // Update last distribution timestamp
        poolData.lastDistributionTimestamp = block.timestamp;

        // Distribute rewards
        for (uint256 i = 0; i < distributions.length; i++) {
            require(distributions[i].amount > 0, "Invalid amount");

            // fixme: interval checke 目前需求是允许重复分配，可能是 tg 或者 twitter 活动的用户
            // require(
            //     lastRewardReceivedTimestamp[_coin][distributions[i].recipient] < currentEpochStart,
            //     RecipientRewardedThisInterval()
            // );

            poolData.ipCoinContract.transferFrom(msg.sender, distributions[i].recipient, distributions[i].amount);

            // Update the last reward received timestamp for the recipient
            lastRewardReceivedTimestamp[_coin][distributions[i].recipient] = block.timestamp;
        }

        emit RewardsDistributed(msg.sender, _coin, totalDistribution, distributions.length);
    }

    /**
     * @notice Set common parameters used by both initialize and migrateNewVersion functions
     * @dev This internal function sets parameters that are common to both functions
     */
    function _setCommonParameters() internal {
        // Halving algorithm parameters
        REWARD_TOKEN_SUPPLY = 200_000_000 * DEFAULT_PRECISION;
        INITIAL_MAX_OUTPUT_PER_INTERVAL = 1_923_076 * DEFAULT_PRECISION;
        HALVING_THRESHOLD = 25_000_008 * DEFAULT_PRECISION;

        BONDING_CURVE_TRADE_CAP = 600_000_000 * DEFAULT_PRECISION;

        // Set default maximum distribution to 10% of wallet balance
        MAX_WALLET_DISTRIBUTION_PERCENTAGE = 1000; // 10% in basis points

        // Set default global reward interval to 1 week
        globalRewardInterval = 1 weeks;
    }

    /**
     * @notice Set chain-specific parameters based on the current blockchain
     * @dev This internal function sets parameters specific to each supported blockchain
     */
    function _setChainSpecificParameters() internal {
        if (block.chainid == 8453 || block.chainid == 84_532) {
            // base mainnet or base sepolia
            LIQUIDITY_NATIVE_AMOUNT = 6.4 ether; // ~6.6 eth(6.59xx) --> 100%
            VIRTUAL_ETH_SUPPLY = 0.1494 ether;
            VIRTUAL_TOKEN_SUPPLY = 106_774_000 * DEFAULT_PRECISION;
            PRICE_CONSTANT = 7_240_000_000; // k 7.24 × 10^-9 with 18 decimal places
        } else if (block.chainid == 97 || block.chainid == 56) {
            // bsc testnet or bsc mainnet
            LIQUIDITY_NATIVE_AMOUNT = 24 ether; // 24.74 bnb -> 25.505 bnb
            VIRTUAL_ETH_SUPPLY = 0.10725 ether;
            VIRTUAL_TOKEN_SUPPLY = 42_187_156 * DEFAULT_PRECISION;
            PRICE_CONSTANT = 15_525_600_000; // k 1.55256 × 10^-8 with 18 decimal places
        } else {
            revert UnsupportedChain();
        }
    }

    receive() external payable {}

    fallback() external payable {}

    // The following functions are overrides required by Solidity.
    function _authorizeUpgrade(
        address newImplementation
    ) internal override onlyRole(ADMIN_ROLE) {}
}
